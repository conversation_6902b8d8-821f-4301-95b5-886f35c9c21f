/**
 * New Video Store using MySQL API
 * Replaces Supabase video queries
 */

import { create } from 'zustand';
import { apiClient, Video } from '../lib/api';

interface VideoItem {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  videoUrl: string;
  duration: number;
  views: number;
  likes: number;
  isHD: boolean;
  category: string;
  uploadDate: string;
  creator: {
    id: string;
    name: string;
    avatar: string;
    isVerified: boolean;
    isCreator: boolean;
    subscriberCount: number;
  };
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
}

interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

interface FilterOptions {
  category?: string;
  status?: string;
}

interface VideoState {
  videos: VideoItem[];
  userVideos: VideoItem[];
  selectedVideoIds: string[];
  sortOptions: SortOptions;
  filterOptions: FilterOptions;
  pagination: Pagination;
  isLoading: boolean;
  error: string | null;
  currentCategory: string;
  searchQuery: string;

  // Actions
  fetchVideos: (category?: string, page?: number) => Promise<void>;
  fetchUserVideos: (sort?: SortOptions, filter?: FilterOptions, page?: number) => Promise<void>;
  searchVideos: (query: string, page?: number) => Promise<void>;
  setCategory: (category: string) => void;
  setPage: (page: number) => Promise<void>;
  setSortOptions: (options: SortOptions) => void;
  setFilterOptions: (options: FilterOptions) => void;
  selectVideo: (id: string) => void;
  selectAllVideos: () => void;
  deleteVideo: (id: string) => Promise<void>;
  batchDeleteVideos: (ids: string[]) => Promise<void>;
  batchUpdateVideosStatus: (ids: string[], status: string) => Promise<void>;
  batchUpdateVideosCategory: (ids: string[], category: string) => Promise<void>;
  clearError: () => void;
  incrementVideoViews: (id: string) => Promise<boolean>;
}

// Helper function to convert API video to VideoItem
const convertApiVideoToVideoItem = (video: Video): VideoItem => ({
  id: video.id,
  title: video.title,
  description: video.description || '',
  thumbnail: video.thumbnail_url || 'https://placehold.co/400x225/gray/white?text=No+Thumbnail',
  videoUrl: video.video_url,
  duration: video.duration || 0,
  views: video.views || 0,
  likes: video.likes || 0,
  isHD: video.is_hd || false,
  category: video.category || 'uncategorized',
  uploadDate: video.created_at,
  creator: {
    id: video.user_id,
    name: video.username || 'Unknown User',
    avatar: video.user_avatar || 'https://placehold.co/150/gray/white?text=User',
    isVerified: false,
    isCreator: true,
    subscriberCount: 0,
  },
});

export const useVideoStore = create<VideoState>((set, get) => ({
  videos: [],
  userVideos: [],
  selectedVideoIds: [],
  sortOptions: {
    field: 'created_at',
    direction: 'desc',
  },
  filterOptions: {},
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 20,
  },
  isLoading: false,
  error: null,
  currentCategory: 'all',
  searchQuery: '',

  fetchVideos: async (category = 'all', page = 1) => {
    try {
      set({ isLoading: true, error: null });

      const response = await apiClient.getVideos({
        page,
        limit: 20,
        category: category === 'all' ? undefined : category,
        sort: 'created_at',
        order: 'DESC',
      });

      if (response.success && response.data.videos) {
        const videos = response.data.videos.map(convertApiVideoToVideoItem);
        
        set({
          videos,
          pagination: {
            currentPage: response.data.pagination.page,
            totalPages: response.data.pagination.pages,
            totalCount: response.data.pagination.total,
            pageSize: response.data.pagination.limit,
          },
          currentCategory: category,
          isLoading: false,
        });
      } else {
        throw new Error('Failed to fetch videos');
      }
    } catch (error) {
      console.error('Error fetching videos:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch videos',
        isLoading: false,
      });
    }
  },

  searchVideos: async (query: string, page = 1) => {
    try {
      set({ isLoading: true, error: null, searchQuery: query });

      const response = await apiClient.getVideos({
        page,
        limit: 20,
        search: query,
        sort: 'created_at',
        order: 'DESC',
      });

      if (response.success && response.data.videos) {
        const videos = response.data.videos.map(convertApiVideoToVideoItem);
        
        set({
          videos,
          pagination: {
            currentPage: response.data.pagination.page,
            totalPages: response.data.pagination.pages,
            totalCount: response.data.pagination.total,
            pageSize: response.data.pagination.limit,
          },
          isLoading: false,
        });
      } else {
        throw new Error('Failed to search videos');
      }
    } catch (error) {
      console.error('Error searching videos:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to search videos',
        isLoading: false,
      });
    }
  },

  setCategory: (category: string) => {
    const { fetchVideos } = get();
    set({ currentCategory: category, searchQuery: '' });
    fetchVideos(category, 1);
  },

  setPage: async (page: number) => {
    const { currentCategory, searchQuery, fetchVideos, searchVideos } = get();
    
    if (searchQuery) {
      await searchVideos(searchQuery, page);
    } else {
      await fetchVideos(currentCategory, page);
    }
  },

  clearError: () => {
    set({ error: null });
  },

  fetchUserVideos: async (sort?: SortOptions, filter?: FilterOptions, page = 1) => {
    try {
      set({ isLoading: true, error: null });

      const params: any = {
        page,
        limit: 20,
        sort: sort?.field || 'created_at',
        order: sort?.direction === 'asc' ? 'ASC' : 'DESC',
        user_only: true, // This will be a flag to get only current user's videos
      };

      if (filter?.category && filter.category !== 'all') {
        params.category = filter.category;
      }

      const response = await apiClient.getVideos(params);

      if (response.success && response.data.videos) {
        const userVideos = response.data.videos.map(convertApiVideoToVideoItem);

        set({
          userVideos,
          pagination: {
            currentPage: response.data.pagination.page,
            totalPages: response.data.pagination.pages,
            totalCount: response.data.pagination.total,
            pageSize: response.data.pagination.limit,
          },
          isLoading: false,
        });
      } else {
        throw new Error('Failed to fetch user videos');
      }
    } catch (error) {
      console.error('Error fetching user videos:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch user videos',
        isLoading: false,
      });
    }
  },

  setSortOptions: (options: SortOptions) => {
    set({ sortOptions: options });
  },

  setFilterOptions: (options: FilterOptions) => {
    set({ filterOptions: options });
  },

  selectVideo: (id: string) => {
    const { selectedVideoIds } = get();
    const isSelected = selectedVideoIds.includes(id);

    set({
      selectedVideoIds: isSelected
        ? selectedVideoIds.filter(videoId => videoId !== id)
        : [...selectedVideoIds, id]
    });
  },

  selectAllVideos: () => {
    const { userVideos, selectedVideoIds } = get();
    const allVideoIds = userVideos.map(video => video.id);

    set({
      selectedVideoIds: selectedVideoIds.length === allVideoIds.length ? [] : allVideoIds
    });
  },

  deleteVideo: async (id: string) => {
    try {
      await apiClient.deleteVideo(id);

      // Remove from userVideos
      const { userVideos, selectedVideoIds } = get();
      set({
        userVideos: userVideos.filter(video => video.id !== id),
        selectedVideoIds: selectedVideoIds.filter(videoId => videoId !== id),
      });
    } catch (error) {
      console.error('Error deleting video:', error);
      throw error;
    }
  },

  batchDeleteVideos: async (ids: string[]) => {
    try {
      // Delete videos one by one (could be optimized with batch API)
      await Promise.all(ids.map(id => apiClient.deleteVideo(id)));

      // Remove from userVideos
      const { userVideos } = get();
      set({
        userVideos: userVideos.filter(video => !ids.includes(video.id)),
        selectedVideoIds: [],
      });
    } catch (error) {
      console.error('Error batch deleting videos:', error);
      throw error;
    }
  },

  batchUpdateVideosStatus: async (ids: string[], status: string) => {
    try {
      // Update videos one by one (could be optimized with batch API)
      await Promise.all(ids.map(id => apiClient.updateVideo(id, { status } as any)));

      // Refresh user videos
      const { fetchUserVideos, sortOptions, filterOptions, pagination } = get();
      await fetchUserVideos(sortOptions, filterOptions, pagination.currentPage);

      set({ selectedVideoIds: [] });
    } catch (error) {
      console.error('Error batch updating video status:', error);
      throw error;
    }
  },

  batchUpdateVideosCategory: async (ids: string[], category: string) => {
    try {
      // Update videos one by one (could be optimized with batch API)
      await Promise.all(ids.map(id => apiClient.updateVideo(id, { category })));

      // Refresh user videos
      const { fetchUserVideos, sortOptions, filterOptions, pagination } = get();
      await fetchUserVideos(sortOptions, filterOptions, pagination.currentPage);

      set({ selectedVideoIds: [] });
    } catch (error) {
      console.error('Error batch updating video category:', error);
      throw error;
    }
  },

  incrementVideoViews: async (id: string) => {
    // Note: The MySQL API automatically increments views when fetching a video
    // This function is kept for compatibility with existing code
    // The actual view increment happens in the backend when getVideo is called
    try {
      console.log('View increment handled automatically by backend for video:', id);
      return true;
    } catch (error) {
      console.error('Error in incrementVideoViews:', error);
      return false;
    }
  },
}));
