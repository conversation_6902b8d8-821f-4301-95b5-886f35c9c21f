<?php
/**
 * Videos API endpoint
 * Handles CRUD operations for videos
 */

require_once 'config/database.php';

// Set CORS headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://www.bluefilmx.com');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session
if (!isset($_SESSION)) {
    session_start();
}

// Helper functions
function getCurrentUser() {
    return $_SESSION['user_id'] ?? null;
}

function requireAuth() {
    $userId = getCurrentUser();
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'Authentication required']);
        exit();
    }
    return $userId;
}

function successResponse($data) {
    echo json_encode(['success' => true, 'data' => $data]);
    exit();
}

function errorResponse($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'error' => $message]);
    exit();
}

function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Get video ID from URL if present
$videoId = isset($pathParts[2]) ? $pathParts[2] : null;

switch ($method) {
    case 'GET':
        if ($videoId) {
            getVideo($db, $videoId);
        } else {
            getVideos($db);
        }
        break;
    
    case 'POST':
        createVideo($db);
        break;
    
    case 'PUT':
        if ($videoId) {
            updateVideo($db, $videoId);
        } else {
            errorResponse('Video ID required for update');
        }
        break;
    
    case 'DELETE':
        if ($videoId) {
            deleteVideo($db, $videoId);
        } else {
            errorResponse('Video ID required for delete');
        }
        break;
    
    default:
        errorResponse('Method not allowed', 405);
}

function getVideos($db) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $category = isset($_GET['category']) ? $_GET['category'] : null;
    $search = isset($_GET['search']) ? $_GET['search'] : null;
    $sortBy = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
    $order = isset($_GET['order']) ? $_GET['order'] : 'DESC';
    $userOnly = isset($_GET['user_only']) ? $_GET['user_only'] : false;

    $offset = ($page - 1) * $limit;
    
    // Build query
    $whereConditions = [];
    $params = [];
    
    if ($category && $category !== 'all') {
        if ($category === 'recommended') {
            // For recommended, we'll use a different sorting logic (most viewed)
            // Don't add a WHERE condition, just change the sorting
        } else {
            $whereConditions[] = "v.category = :category";
            $params[':category'] = $category;
        }
    }
    
    if ($search) {
        $whereConditions[] = "(v.title LIKE :search1 OR v.description LIKE :search2)";
        $params[':search1'] = '%' . $search . '%';
        $params[':search2'] = '%' . $search . '%';
    }

    // Filter by current user if requested
    if ($userOnly) {
        $userId = getCurrentUser();
        if (!$userId) {
            errorResponse('Authentication required for user-only videos', 401);
        }
        $whereConditions[] = "v.user_id = :user_id";
        $params[':user_id'] = $userId;
    }

    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Handle special sorting for recommended category
    if ($category === 'recommended') {
        // For recommended, sort by views (most viewed first)
        $orderClause = "ORDER BY v.views DESC, v.created_at DESC";
    } else {
        // Validate sort column
        $allowedSorts = ['created_at', 'views', 'likes', 'title'];
        if (!in_array($sortBy, $allowedSorts)) {
            $sortBy = 'created_at';
        }

        $orderClause = "ORDER BY v.$sortBy $order";
    }
    
    try {
        // Get total count
        $countQuery = "SELECT COUNT(*) as total FROM videos v $whereClause";
        $countStmt = $db->prepare($countQuery);

        // Bind parameters for count query
        foreach ($params as $key => $value) {
            $countStmt->bindValue($key, $value);
        }
        $countStmt->execute();
        $total = $countStmt->fetch()['total'];

        // Get videos with user info
        $query = "
            SELECT
                v.*,
                p.username,
                p.avatar_url as user_avatar
            FROM videos v
            LEFT JOIN profiles p ON v.user_id = p.id
            $whereClause
            $orderClause
            LIMIT :limit OFFSET :offset
        ";

        $stmt = $db->prepare($query);

        // Bind parameters for main query
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();
        $videos = $stmt->fetchAll();
        
        // Convert boolean fields
        foreach ($videos as &$video) {
            $video['is_hd'] = (bool)$video['is_hd'];
            $video['views'] = (int)$video['views'];
            $video['likes'] = (int)$video['likes'];
            $video['duration'] = (int)$video['duration'];
        }
        
        successResponse([
            'videos' => $videos,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => (int)$total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Error fetching videos: " . $e->getMessage());
        errorResponse('Failed to fetch videos', 500);
    }
}

function getVideo($db, $videoId) {
    try {
        // Increment view count
        $updateQuery = "UPDATE videos SET views = views + 1 WHERE id = :id";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->bindParam(':id', $videoId);
        $updateStmt->execute();
        
        // Get video with user info
        $query = "
            SELECT 
                v.*,
                p.username,
                p.avatar_url as user_avatar
            FROM videos v
            LEFT JOIN profiles p ON v.user_id = p.id
            WHERE v.id = :id
        ";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $videoId);
        $stmt->execute();
        
        $video = $stmt->fetch();
        
        if (!$video) {
            errorResponse('Video not found', 404);
        }
        
        // Convert boolean fields
        $video['is_hd'] = (bool)$video['is_hd'];
        $video['views'] = (int)$video['views'];
        $video['likes'] = (int)$video['likes'];
        $video['duration'] = (int)$video['duration'];
        
        successResponse($video);
        
    } catch (Exception $e) {
        error_log("Error fetching video: " . $e->getMessage());
        errorResponse('Failed to fetch video', 500);
    }
}

function createVideo($db) {
    $userId = requireAuth();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['title']) || !isset($input['video_url'])) {
        errorResponse('Title and video URL are required');
    }
    
    try {
        $query = "
            INSERT INTO videos (
                id, title, description, thumbnail_url, video_url, 
                duration, category, user_id, created_at, updated_at
            ) VALUES (
                :id, :title, :description, :thumbnail_url, :video_url,
                :duration, :category, :user_id, NOW(), NOW()
            )
        ";
        
        $stmt = $db->prepare($query);
        
        $videoId = generateUUID();
        $stmt->bindParam(':id', $videoId);
        $stmt->bindParam(':title', $input['title']);
        $stmt->bindParam(':description', $input['description'] ?? null);
        $stmt->bindParam(':thumbnail_url', $input['thumbnail_url'] ?? null);
        $stmt->bindParam(':video_url', $input['video_url']);
        $stmt->bindParam(':duration', $input['duration'] ?? 0);
        $stmt->bindParam(':category', $input['category'] ?? 'uncategorized');
        $stmt->bindParam(':user_id', $userId);
        
        $stmt->execute();
        
        successResponse(['id' => $videoId, 'message' => 'Video created successfully']);
        
    } catch (Exception $e) {
        error_log("Error creating video: " . $e->getMessage());
        errorResponse('Failed to create video', 500);
    }
}

function updateVideo($db, $videoId) {
    $userId = requireAuth();
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        errorResponse('Invalid input data');
    }
    
    try {
        // Check if user owns the video
        $checkQuery = "SELECT user_id FROM videos WHERE id = :id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':id', $videoId);
        $checkStmt->execute();
        $video = $checkStmt->fetch();
        
        if (!$video) {
            errorResponse('Video not found', 404);
        }
        
        if ($video['user_id'] !== $userId) {
            errorResponse('Unauthorized', 403);
        }
        
        // Build update query dynamically
        $updateFields = [];
        $params = [':id' => $videoId];
        
        $allowedFields = ['title', 'description', 'thumbnail_url', 'category'];
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = :$field";
                $params[":$field"] = $input[$field];
            }
        }
        
        if (empty($updateFields)) {
            errorResponse('No valid fields to update');
        }
        
        $updateFields[] = "updated_at = NOW()";
        
        $query = "UPDATE videos SET " . implode(', ', $updateFields) . " WHERE id = :id";
        
        $stmt = $db->prepare($query);
        $stmt->execute($params);
        
        successResponse(['message' => 'Video updated successfully']);
        
    } catch (Exception $e) {
        error_log("Error updating video: " . $e->getMessage());
        errorResponse('Failed to update video', 500);
    }
}

function deleteVideo($db, $videoId) {
    $userId = requireAuth();
    
    try {
        // Check if user owns the video
        $checkQuery = "SELECT user_id FROM videos WHERE id = :id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':id', $videoId);
        $checkStmt->execute();
        $video = $checkStmt->fetch();
        
        if (!$video) {
            errorResponse('Video not found', 404);
        }
        
        if ($video['user_id'] !== $userId) {
            errorResponse('Unauthorized', 403);
        }
        
        $query = "DELETE FROM videos WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $videoId);
        $stmt->execute();
        
        successResponse(['message' => 'Video deleted successfully']);
        
    } catch (Exception $e) {
        error_log("Error deleting video: " . $e->getMessage());
        errorResponse('Failed to delete video', 500);
    }
}
?>
