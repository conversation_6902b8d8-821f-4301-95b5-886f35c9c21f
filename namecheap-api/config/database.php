<?php
/**
 * Database configuration for BlueFilmX
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'bluerpcm_bluefilmx'; // Update this with your actual database name
    private $username = 'bluerpcm_dbuser';   // Update this with your actual username
    private $password = 'kingpatrick100';      // Updated with actual password
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]
            );
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed");
        }

        return $this->conn;
    }
}

/**
 * CORS and JSON response helpers
 */
function setCORSHeaders() {
    // Get the origin of the request
    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';

    // Debug log for troubleshooting (remove in production)
    error_log("CORS Debug - Origin: " . $origin . " | Request Method: " . $_SERVER['REQUEST_METHOD']);

    // List of allowed origins
    $allowedOrigins = [
        'https://www.bluefilmx.com',
        'https://bluefilmx.com',
        'http://localhost:3000',
        'http://localhost:5173'
    ];

    // Check if the origin is allowed
    if (in_array($origin, $allowedOrigins)) {
        header("Access-Control-Allow-Origin: " . $origin);
    } else {
        // For production, be more restrictive - only allow known domains
        // Check if it's a bluefilmx.com variant
        if (preg_match('/^https:\/\/(www\.)?bluefilmx\.com$/', $origin)) {
            header("Access-Control-Allow-Origin: " . $origin);
        } else {
            // Default to the main domain for any other case
            header("Access-Control-Allow-Origin: https://www.bluefilmx.com");
        }
    }

    header("Content-Type: application/json; charset=UTF-8");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Max-Age: 3600");
    header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

    // Handle preflight OPTIONS request
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

function jsonResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit();
}

function errorResponse($message, $status = 400) {
    jsonResponse(['error' => $message], $status);
}

function successResponse($data) {
    jsonResponse(['data' => $data, 'success' => true]);
}

/**
 * Simple authentication helper
 */
function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

/**
 * Simple session-based authentication
 */
function getCurrentUser() {
    session_start();
    return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
}

function requireAuth() {
    $userId = getCurrentUser();
    if (!$userId) {
        errorResponse('Authentication required', 401);
    }
    return $userId;
}

function loginUser($userId) {
    session_start();
    $_SESSION['user_id'] = $userId;
}

function logoutUser() {
    session_start();
    session_destroy();
}
?>
