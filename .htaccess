# BlueFilmX - Apache Configuration for Single Page Application
# Handles client-side routing for React Router

RewriteEngine On

# Handle Angular and React Router
# If the requested resource doesn't exist as a file or directory,
# serve the index.html file instead
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{REQUEST_URI} !^/media/
RewriteRule . /index.html [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static assets but not HTML/JS for debugging
<IfModule mod_expires.c>
    ExpiresActive on
    # Don't cache HTML and JS files to prevent old code issues
    ExpiresByType text/html "access plus 0 seconds"
    ExpiresByType application/javascript "access plus 0 seconds"
    # Cache other static assets
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType video/mp4 "access plus 1 year"
    ExpiresByType video/webm "access plus 1 year"
</IfModule>

# Additional cache control headers
<IfModule mod_headers.c>
    # Prevent caching of HTML and JS files
    <FilesMatch "\.(html|js)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>

# CORS headers for API are handled by PHP setCORSHeaders() function
# This allows for proper credential handling with specific origins
<IfModule mod_headers.c>
    # Only set CORS for non-PHP files if needed
    # PHP files handle their own CORS via setCORSHeaders()
</IfModule>

# Handle preflight OPTIONS requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
